/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
apply plugin: 'io.codearte.nexus-staging'

buildscript {
    ext.kotlin_version = '1.8.21'
    ext.nexus_plugin_version = '0.30.0'
    ext.hydra_client_plugin = '1.1.4'
    
    repositories {
        google()
        jcenter()
        maven {
            url "https://maven.google.com"
        }
        maven{
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "io.codearte.gradle.nexus:gradle-nexus-staging-plugin:$nexus_plugin_version"
        classpath "com.microsoft.hydralab:gradle_plugin:$hydra_client_plugin"
    }
}

apply plugin: "com.microsoft.hydralab.client-util"

allprojects {
    project.ext {
        constants = [
                minSdkVersion: 23,
                targetSdkVersion: 34,
                compileSdkVersion: 34
        ]

        androidxRunner            =    '1.5.0'
        androidxCoreKt            =    '1.10.1'
        androidTestMonitor        =    '1.6.1'
        appCenterSdkVersion       =    '5.0.2'
        appCompatVersion          =    '1.6.1'
        buildToolVersion          =    '31.0.0'
        composeActivityVersion    =    '1.7.2'
        composeBomVersion         =    '2023.09.00'
        composeTestVersion        =    '1.4.3'
        composeCompilerVersion    =    '1.4.7'
        constraintLayoutVersion   =    '2.1.4'
        constraintLayoutComposeVersion   =    '1.0.1'
        espressoVersion           =    '3.5.1'
        exifInterfaceVersion      =    '1.3.6'
        extJunitVersion           =    '1.1.5'
        hamcrestVersion           =    '2.2'
        junitVersion              =    '4.12'
        junitKtxVersion           =    '1.1.5'
        kotlin_version            =    '1.8.21'
        lifecycleVersion          =    '2.4.1'
        materialVersion           =    '1.9.0'
        mockitoVersion            =    '1.10.19'
        recyclerViewVersion       =    '1.3.0'
        robolectric               =    '4.13'
        uiautomatorVersion        =    '2.2.0'
        supportVersion            =    '28.0.0'
        universalPkgDir           =    "universal"
        composeFoundationVersion  =    '1.6.0'
    }
    repositories {
        google()
        jcenter()
    }
}

task makeUniversalPkg(type: Copy) {
    duplicatesStrategy = DuplicatesStrategy.WARN
    from('.')
    include('**/*.aar')
    exclude('**/FluentUI-release.aar')
    exclude("$universalPkgDir")
    into("$rootDir/$universalPkgDir/libs")
    includeEmptyDirs = false
    eachFile {
        path = name
    }
    rename '(.+)-release', '$1'
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
