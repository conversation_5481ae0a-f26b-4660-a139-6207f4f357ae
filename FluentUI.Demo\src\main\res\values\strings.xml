<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) Microsoft Corporation. All rights reserved.
  ~ Licensed under the MIT License.
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

    <!--App-->
    <string name="app_name">Fluent UI Demo</string>
    <string name="app_title">Fluent UI</string>
    <!-- Announces selected component -->
    <string name="app_accessibility_selected">Selected %s</string>
    <string name="app_modifiable_parameters">Modifiable Parameters</string>
    <string name="app_right_accessory_view">Right Accessory View</string>

    <string name="app_style">Style</string>
    <!-- describes an action, icon pressed -->
    <string name="app_icon_pressed">Icon Pressed</string>

    <!--ActionBar-->
    <!-- Text for button that starts the demo -->
    <string name="actionbar_start_button">Start Demo</string>
    <!-- Texts for radio buttons -->
    <string name="actionbar_carousel_radio_label">Carousel</string>
    <string name="actionbar_icon_radio_label">Icon</string>
    <string name="actionbar_basic_radio_label">Basic</string>
    <string name="actionbar_position_bottom_radio_label">Bottom</string>
    <string name="actionbar_position_top_radio_label">Top</string>

    <!--UI Text view label denoting type of action bar -->
    <string name="actionbar_type_heading">ActionBar Type</string>
    <!--UI Text view label denoting position of action bar -->
    <string name="actionbar_position_heading">ActionBar Position</string>

    <!--AppBar-->
    <string name="app_bar_style">AppBar Style</string>
    <string name="app_bar_subtitle">Subtitle</string>

    <!-- Text to show the bottom border property of App bar .-->
    <string name="app_bar_bottom_border">Bottom Border</string>

    <!-- Text that shows the action performed to the user -->
    <string name="app_bar_layout_navigation_icon_clicked">Navigation icon clicked.</string>

    <!-- Text for title alignment -->
    <string name="center_title_alignment">Center align app bar</string>

    <!-- Text for app bar size -->
    <string name="app_bar_size">App Bar size</string>

    <!-- Text for app bar left accessories -->
    <string name="left_logo">Left Logo</string>

    <!-- Text for app bar navigation icon -->
    <string name="navigation_icon">Navigation Icon</string>

    <!-- Text for enabling tooltips -->
    <string name="enable_tooltips">Enable Tooltips</string>

    <!-- Icon labels -->
    <string name="app_bar_layout_menu_flag">Flag</string>
    <string name="app_bar_layout_menu_settings">Settings</string>
    <string name="app_bar_layout_menu_search">Search</string>
    <!-- UI Text that shows the type of scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_sub_header">Scroll behavior: %s</string>
    <!-- Text for button that toggles the scroll behavior -->
    <string name="app_bar_layout_toggle_scroll_behavior_button">Toggle scroll behavior</string>
    <!-- UI label for toggling navigation icon -->
    <string name="app_bar_layout_toggle_navigation_icon_sub_header">Toggle navigation icon</string>
    <!-- Text for button that shows avatar -->
    <string name="app_bar_layout_show_avatar_button">Show avatar</string>
    <!-- Text for button that shows back icon -->
    <string name="app_bar_layout_show_back_icon_button">Show back icon</string>
    <!-- Text for button that hides icon-->
    <string name="app_bar_layout_hide_icon_button">Hide icon</string>
    <!-- Text for button that shows icon -->
    <string name="app_bar_layout_show_icon_button">Show icon</string>
    <!-- UI label denoting layout style -->
    <string name="app_bar_layout_toggle_searchbar_sub_header">Toggle searchbar layout style</string>
    <!-- Text for button that shows searchbar as accessory or action view -->
    <string name="app_bar_layout_searchbar_accessory_view_button">Show as accessory view</string>
    <string name="app_bar_layout_searchbar_action_view_button">Show as action view</string>
    <!-- UI label for toggling themes -->
    <string name="app_bar_layout_toggle_theme_sub_header">Toggle between themes (recreates activity)</string>
    <!-- Text for button that toggles themes -->
    <string name="app_bar_layout_toggle_theme_button">Toggle theme</string>
    <!-- Label for list -->
    <string name="app_bar_layout_list_item">Item</string>
    <!-- UI label that denotes extra content that is scrollable -->
    <string name="app_bar_layout_list_sub_header">Extra scrollable content</string>

    <!--AvatarView-->
    <!-- Style -->
    <string name="avatar_style_circle">Circle style</string>
    <string name="avatar_style_square">Square style</string>
    <!-- Sizes -->
    <string name="avatar_size_xxlarge">XXLarge</string>
    <string name="avatar_size_xlarge">XLarge</string>
    <string name="avatar_size_large">Large</string>
    <string name="avatar_size_medium">Medium</string>
    <string name="avatar_size_small">Small</string>
    <string name="avatar_size_xsmall">XSmall</string>
    <!-- Accessibility -->
    <string name="avatar_size_xxlarge_accessibility">Double Extra Large</string>
    <string name="avatar_size_xlarge_accessibility">Extra Large</string>
    <string name="avatar_size_xsmall_accessibility">Extra Small</string>

    <!--AvatarGroupView-->
    <!-- UI label to show maximum displayed avatars -->
    <string name="avatar_group_max_displayed_avatar">Max Displayed Avatar</string>
    <!-- UI label to show the overflow avatar counter -->
    <string name="avatar_group_overflow_avatar_count">Overflow Avatar Count</string>
    <!-- UI label to show border type -->
    <string name="avatar_group_border_type">Border Type</string>
    <!-- UI Info explaining OverflowAvatarCount will not adhere to maximum displayed avatars -->
    <string name="avatar_group_info">Avatar Group with OverflowAvatarCount set will not adhere to max Displayed Avatar.</string>
    <!-- UI label showing face stack type of avatar group -->
    <string name="avatar_group_face_stack">Face Stack</string>
    <!-- UI label showing face pile type of avatar group -->
    <string name="avatar_group_face_pile">Face Pile</string>
    <!-- UI label denoting the overflow clicked action performed by the user -->
    <string name="avatar_group_overflow_clicked">Overflow clicked</string>
    <!-- UI label denoting the avatar view clicked action performed by the user at a specific index-->
    <string name="avatar_group_avatar_clicked">AvatarView at index %d clicked</string>

    <!--Badge-->
    <!--UI Label for notification badge  -->
    <string name="badge_notification_badge">Notification Badge</string>
    <!--UI Label for dot notification -->
    <string name="badge_notification_dot">Dot</string>
    <!--UI Label for notification shown as list type used in horizontal tab -->
    <string name="badge_notification_list">List</string>
    <!--UI Label for notification shown in character used in vertical Tab -->
    <string name="badge_notification_character">Character</string>

    <!--BottomNavigation-->
    <!--UI Labels -->
    <string name="bottom_navigation_menu_item_photos">Photos</string>
    <string name="bottom_navigation_menu_item_news">News</string>
    <string name="bottom_navigation_menu_item_alerts">Alerts</string>
    <string name="bottom_navigation_menu_item_calendar">Calendar</string>
    <string name="bottom_navigation_menu_item_team">Team</string>
    <!-- Text for buttons -->
    <string name="bottom_navigation_toggle_labels_button">Toggle labels</string>
    <string name="bottom_navigation_three_menu_items_button">Show three menu items</string>
    <string name="bottom_navigation_four_menu_items_button">Show four menu items</string>
    <string name="bottom_navigation_five_menu_items_button">Show five menu items</string>
    <!--accessibility-->
    <!-- Announces if labels are ON/OFF -->
    <string name="bottom_navigation_accessibility_labels_state">Labels are %s</string>

    <!--BottomSheet-->
    <!--UI Labels -->
    <string name="bottom_sheet">BottomSheet</string>
    <string name="bottom_sheet_dialog">BottomSheetDialog</string>
    <string name ="bottom_sheet_text_enable_swipe_dismiss">Enable Swipe Down to Dismiss</string>
    <!-- Text for buttons -->
    <string name="bottom_sheet_with_single_line_items">Show with single line items</string>
    <string name="bottom_sheet_with_double_line_items">Show with double line items</string>
    <string name="bottom_sheet_with_single_line_header">Show with single line header</string>
    <string name="bottom_sheet_with_double_line_title_and_dividers">Show with double line header and dividers</string>
    <string name="bottom_sheet_dialog_button">Show</string>
    <string name="drawer_content_desc_collapse_state">Expand</string>
    <string name="drawer_content_desc_expand_state">Minimize</string>
    <string name="large_scrollable_text" translatable="false">Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
        Large demo Text line for showing scroll behaviour of a content. Use inside a Scrollview \n
    </string>

    <!--ContextualCommandBar-->
    <!-- UI text showing the click action performed by the user -->
    <string name="contextual_command_prompt_click_item">Click %s</string>
    <!-- UI text showing the long click action performed by the user -->
    <string name="contextual_command_prompt_long_click_item">Long Click %s</string>
    <!-- UI text showing the click on dismiss action performed by the user -->
    <string name="contextual_command_prompt_click_dismiss">Click dismiss</string>
    <!-- Text for button for inserting an item-->
    <string name="contextual_command_insert_item">Insert Item</string>
    <!-- Text for button for updating an item-->
    <string name="contextual_command_update_item">Update Item</string>
    <!-- Announces dismiss action -->
    <string name="contextual_command_accessibility_dismiss">Dismiss</string>
    <!-- Announces add action -->
    <string name="contextual_command_accessibility_add">Add</string>
    <!-- Announces mention action -->
    <string name="contextual_command_accessibility_mention">Mention</string>
    <!-- Announces bold action -->
    <string name="contextual_command_accessibility_bold">Bold</string>
    <!-- Announces italic action -->
    <string name="contextual_command_accessibility_italic">Italic</string>
    <!-- Announces undeline action -->
    <string name="contextual_command_accessibility_underline">Underline</string>
    <!-- Announces strikethrough action -->
    <string name="contextual_command_accessibility_strikethrough">Strikethrough</string>
    <!-- Announces undo action -->
    <string name="contextual_command_accessibility_undo">Undo</string>
    <!-- Announces redo action -->
    <string name="contextual_command_accessibility_redo">Redo</string>
    <!-- Announces bullet action -->
    <string name="contextual_command_accessibility_bullet">Bullet</string>
    <!-- Announces list action -->
    <string name="contextual_command_accessibility_list">List</string>
    <!-- Announces link action -->
    <string name="contextual_command_accessibility_link">Link</string>
    <!-- UI Label denoting item updating -->
    <string name="contextual_command_bar_item_update_title">Item Updating</string>
    <!-- UI Label denoting spacing -->
    <string name="contextual_command_bar_space_title">Spacing</string>
    <!-- UI Label denoting dismiss position -->
    <string name="contextual_command_bar_dismiss_position">Dismiss Position</string>
    <!-- Radio button label START -->
    <string name="contextual_command_bar_dismiss_position_start">START</string>
    <!-- Radio button label END -->
    <string name="contextual_command_bar_dismiss_position_end">END</string>
    <!-- UI bar label denoting the space between groups -->
    <string name="contextual_command_bar_group_space_title">Group space</string>
    <!-- UI bar label denoting the space between items -->
    <string name="contextual_command_bar_item_space_title">Item space</string>
    <!-- UI bar label denoting the space in number value dp's -->
    <string name="contextual_command_bar_space_value">%d dp</string>

    <!--Single line items-->
    <!--UI Label for item flag-->
    <string name="bottom_sheet_item_flag_title">Flag</string>
    <!--UI Label for user action clicked on item flag-->
    <string name="bottom_sheet_item_flag_toast">Flag item clicked</string>
    <!--UI Label for item reply-->
    <string name="bottom_sheet_item_reply_title">Reply</string>
    <!--UI Label for user action clicked on item reply-->
    <string name="bottom_sheet_item_reply_toast">Reply item clicked</string>
    <!--UI Label for item forward-->
    <string name="bottom_sheet_item_forward_title">Forward</string>
    <!--UI Label for user action clicked on item forward-->
    <string name="bottom_sheet_item_forward_toast">Forward item clicked</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_delete_title">Delete</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_delete_toast">Delete item clicked</string>
    <!--UI Label for item toggle-->
    <string name="bottom_sheet_item_toggle_title">Toggle</string>
    <!--UI Label for user action clicked on item toggle-->
    <string name="bottom_sheet_item_toggle_toast">Toggle item clicked</string>
    <!--UI Label for item avatar-->
    <string name="bottom_sheet_item_custom_image">Avatar</string>
    <!--Double line items-->
    <!--UI Label for item camera -->
    <string name="bottom_sheet_item_camera_title">Camera</string>
    <!--UI description label take a photo-->
    <string name="bottom_sheet_item_camera_subtitle">Take a photo</string>
    <!--UI Label for user action clicked on item camera-->
    <string name="bottom_sheet_item_camera_toast">Camera item clicked</string>
    <!--UI Label for item gallery-->
    <string name="bottom_sheet_item_gallery_title">Gallery</string>
    <!--UI description label view your photos-->
    <string name="bottom_sheet_item_gallery_subtitle">View your photos</string>
    <!--UI Label for user action clicked on item gallery-->
    <string name="bottom_sheet_item_gallery_toast">Gallery item clicked</string>
    <!--UI Label for item videos-->
    <string name="bottom_sheet_item_videos_title">Videos</string>
    <!--UI description label play your videos -->
    <string name="bottom_sheet_item_videos_subtitle">Play your videos</string>
    <!--UI Label for user action clicked on item videos-->
    <string name="bottom_sheet_item_videos_toast">Videos item clicked</string>
    <!--UI Label for item manage-->
    <string name="bottom_sheet_item_manage_title">Manage</string>
    <!--UI description label Manage your media library-->
    <string name="bottom_sheet_item_manage_subtitle">Manage your media library</string>
    <!--UI Label for user action clicked on item manage-->
    <string name="bottom_sheet_item_manage_toast">Manage item clicked</string>
    <!--Single line header-->
    <!--UI title header email actions-->
    <string name="bottom_sheet_item_single_line_header">Email Actions</string>
    <!--Double line header-->
    <!--UI title for item documents -->
    <string name="bottom_sheet_item_double_line_header">Documents</string>
    <!--UI subtitle header-->
    <string name="bottom_sheet_item_double_line_header_subtitle">Last updated 2:14PM</string>
    <!--UI Label for item share-->
    <string name="bottom_sheet_item_double_line_header_share">Share</string>
    <!--UI Label for user action clicked on item share-->
    <string name="bottom_sheet_item_double_line_header_share_toast">Share item clicked</string>
    <!--UI Label for item move-->
    <string name="bottom_sheet_item_double_line_header_move">Move</string>
    <!--UI Label for user action clicked on item move-->
    <string name="bottom_sheet_item_double_line_header_move_toast">Move item clicked</string>
    <!--UI Label for item delete-->
    <string name="bottom_sheet_item_double_line_header_delete">Delete</string>
    <!--UI Label for user action clicked on item delete-->
    <string name="bottom_sheet_item_double_line_header_delete_toast">Delete item clicked</string>
    <!--UI Label for item info-->
    <string name="bottom_sheet_item_double_line_header_info">Info</string>
    <!--UI Label for user action clicked on item info-->
    <string name="bottom_sheet_item_double_line_header_info_toast">Info item clicked</string>
    <!--BottomSheetDialog-->
    <!--UI Label for item clock-->
    <string name="bottom_sheet_item_clock_title">Clock</string>
    <!--UI Label for user action clicked on item clock-->
    <string name="bottom_sheet_item_clock_toast">Clock item clicked</string>
    <!--UI Label for item alarm-->
    <string name="bottom_sheet_item_alarm_title">Alarm</string>
    <!--UI Label for user action clicked on item alarm-->
    <string name="bottom_sheet_item_alarm_toast">Alarm item clicked</string>
    <!--UI Label for item timezone-->
    <string name="bottom_sheet_item_time_zone_title">Time zone</string>
    <!--UI Label for user action clicked on item timezone-->
    <string name="bottom_sheet_item_time_zone_toast">Time zone item clicked</string>

    <!--Button-->
    <!-- text for buttons -->
    <string name="basic_input_activity_heading">Different views of button</string>
    <string name="button">Button</string>
    <string name="buttonbar">ButtonBar</string>
    <string name="button_disabled">Disabled Button Example</string>
    <string name="button_borderless">Borderless Button Example</string>
    <string name="button_borderless_disabled">Borderless Disabled Button Example</string>
    <string name="button_large">Large Button Example</string>
    <string name="button_large_disabled">Large Disabled Button Example</string>
    <string name="button_outlined">Outlined Button Example</string>
    <string name="button_outlined_disabled">Outlined Disabled Button Example</string>

    <!--CalendarView-->
    <string name="calendar_example_chosen_date">Choose a date</string>

    <!--DateTimePicker-->
    <!-- title -->
    <string name="date_time_picker_title">DateTimePicker</string>
    <!-- UI label -->
    <string name="date_time_picker_single_date_title">Single Date</string>
    <!-- UI label -->
    <string name="date_time_picker_date_picked_default">No date picked</string>
    <!-- text for button that shows date picker -->
    <string name="date_time_picker_date_button">Show date picker</string>
    <!-- text for button that shows date picker with date tab selected -->
    <string name="date_time_picker_calendar_date_time_button">Show date time picker with date tab selected</string>
    <!-- text for button that shows date picker with time tab selected -->
    <string name="date_time_picker_date_time_calendar_button">Show date time picker with time tab selected</string>
    <!-- text for button that shows time picker -->
    <string name="date_time_picker_date_time_button">Show date time picker</string>
    <!-- UI label to show date range-->
    <string name="date_time_picker_date_range_title">Date Range</string>
    <!-- UI label showing start of the range-->
    <string name="date_time_picker_date_range_start_title">Start:</string>
    <!-- UI label showing end of the range-->
    <string name="date_time_picker_date_range_end_title">End:</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_start_picked_default">No start picked</string>
    <!-- UI label showing no date picked-->
    <string name="date_time_picker_date_end_picked_default">No end picked</string>
    <!-- text for button that selects start date -->
    <string name="date_time_picker_date_start_button">Select start date</string>
    <!-- text for button that selects end date -->
    <string name="date_time_picker_date_end_button">Select end date</string>
    <!-- UI label showing date time range-->
    <string name="date_time_picker_date_time_range_title">Date Time Range</string>
    <!-- text for button that selects date time range -->
    <string name="date_time_picker_date_time_range_button">Select date time range</string>
    <!-- UI label -->
    <string name="date_time_picker_dialog_title">DateTimePickerDialog</string>
    <!-- text for button that shows picker dialog -->
    <string name="date_time_picker_dialog_button">Show Dialog</string>

    <!--Drawer-->
    <!-- text for button that shows drawer -->
    <string name="drawer_button">Show drawer</string>
    <!-- text for button that shows drawer dialog -->
    <string name="drawer_dialog_button">Show drawer dialog</string>
    <!-- text for button that shows no fade bottom dialog -->
    <string name="no_fade_bottom_dialog_button">No fade bottom dialog</string>
    <!-- text for button that shows top drawer -->
    <string name="drawer_top_dialog_button">Show top drawer</string>
    <!-- text for button that shows No fade top dialog drawer -->
    <string name="no_fade_top_dialog_button">No fade top dialog</string>
    <!-- text for button that shows anchor view top dialog drawer -->
    <string name="anchor_view_top_dialog_button"> Show anchor view top dialog</string>
    <!-- text for button that shows no title top dialog drawer -->
    <string name="no_title_top_dialog_button"> Show no title top dialog</string>
    <!-- text for button that shows below title top dialog drawer -->
    <string name="below_title_top_dialog_button"> Show below title top dialog</string>
    <!-- text for button that shows right drawer -->
    <string name="drawer_right_dialog_button">Show right drawer</string>
    <!-- text for button that shows left drawer -->
    <string name="drawer_left_dialog_button">Show left drawer</string>

    <!--ListItem-->
    <!-- Title -->
    <string name="list_item_title">Title, primary text</string>
    <!-- subtitle -->
    <string name="list_item_subtitle">Subtitle, secondary text</string>
    <!-- secondary subtitle -->
    <string name="list_item_secondary_subtitle">Custom subtitle text</string>
    <!-- Footer -->
    <string name="list_item_footer">Footer, tertiary text</string>
    <!-- UI labels -->
    <string name="list_item_sub_header_single_line">Single-line list with gray sub header text</string>
    <string name="list_item_sub_header_two_line">Two-line list</string>
    <string name="list_item_sub_header_two_line_dense">Two-line list with dense spacing</string>
    <string name="list_item_sub_header_two_line_custom_secondary_subtitle">Two line list with custom secondary subtitle view</string>
    <string name="list_item_sub_header_three_line">Three-line list with black sub header text</string>
    <string name="list_item_sub_header_no_custom_views">List items with no custom views</string>
    <string name="list_item_sub_header_large_header">List items with large custom views</string>
    <string name="list_item_sub_header_wrapped_text">List items with wrapped text</string>
    <string name="list_item_sub_header_truncated_text">List items with truncated text</string>
    <string name="list_item_sub_header_custom_accessory_text">Action</string>
    <string name="list_item_truncation_middle">Middle truncation.</string>
    <string name="list_item_truncation_end">End truncation.</string>
    <string name="list_item_truncation_start">Start truncation.</string>
    <string name="list_item_custom_text_view">Value</string>
    <string name="list_item_click">You clicked on the list item.</string>
    <string name="list_item_click_custom_accessory_view">You clicked on the custom accessory view.</string>
    <string name="list_item_click_sub_header_custom_accessory_view">You clicked on the sub header custom accessory view.</string>
    <string name="list_item_more_options">More options</string>

    <!--PeoplePicker-->
    <!-- UI labels -->
    <string name="people_picker_select_example">Select</string>
    <string name="people_picker_select_deselect_example">SelectDeselect</string>
    <string name="people_picker_none_example">None</string>
    <string name="people_picker_delete_example">Delete</string>
    <string name="people_picker_custom_persona_description">This example shows how to create a custom IPersona object.</string>
    <string name="people_picker_dialog_title_removed">You removed a persona:</string>
    <string name="people_picker_dialog_title_added">You added a persona:</string>
    <string name="people_picker_drag_started">Drag started</string>
    <string name="people_picker_drag_ended">Drag ended</string>
    <string name="people_picker_picked_personas_listener">Personas Listener</string>
    <string name="people_picker_suggestions_listener">Suggestions Listener</string>
    <string name="people_picker_persona_chip_click">You clicked on %s</string>

    <plurals name="people_picker_accessibility_text_view_example">
        <item quantity="one">%1$s recipient</item>
        <item quantity="other">%1$s recipients</item>
    </plurals>

    <!--Persistent BottomSheet-->
    <string name="expand_bottom_sheet_button">Expand Persistent Bottom Sheet</string>
    <string name="collapse_persistent_sheet_button"> Hide Persistent Bottom Sheet</string>
    <string name="show_persistent_sheet_button"> Show Persistent Bottom Sheet</string>
    <string name="new_view">This is New View</string>
    <string name="toggle_sheet_content">Toggle Bottomsheet Content</string>
    <string name="switch_to_custom_content">Switch to custom Content</string>
    <string name="one_line_content">One Line Bottomsheet Content</string>
    <string name="toggle_disable_all_items">Toggle Disable All Items</string>
    <!--Persistent BottomSheet Items-->
    <string name="persistent_sheet_item_add_remove_view">Add/Remove View</string>
    <string name="persistent_sheet_item_change_collapsed_height"> Change Collapsed Height</string>
    <string name="persistent_sheet_item_create_new_folder_title">New Folder</string>
    <string name="persistent_sheet_item_create_new_folder_toast">New Folder item clicked</string>
    <string name="persistent_sheet_item_edit_title">Edit</string>
    <string name="persistent_sheet_item_edit_toast">Edit item clicked</string>
    <string name="persistent_sheet_item_save_title">Save</string>
    <string name="persistent_sheet_item_save_toast">Save item clicked</string>
    <string name="persistent_sheet_item_zoom_in_title">Zoom In</string>
    <string name="persistent_sheet_item_zoom_in_toast"> Zoom In item clicked</string>
    <string name="persistent_sheet_item_zoom_out_title">Zoom Out</string>
    <string name="persistent_sheet_item_zoom_out_toast">Zoom Out item clicked</string>

    <!--Persona-->
    <!-- Emails -->
    <string name="persona_email_allan_munger"><EMAIL></string>
    <string name="persona_email_amanda_brady"><EMAIL></string>
    <string name="persona_email_carlos_slattery"><EMAIL></string>
    <string name="persona_email_carole_poland"><EMAIL></string>
    <string name="persona_email_cecil_folk"><EMAIL></string>
    <string name="persona_email_celeste_burton"><EMAIL></string>
    <string name="persona_email_charlotte_waltson"><EMAIL></string>
    <string name="persona_email_colin_ballinger"><EMAIL></string>
    <string name="persona_email_daisy_phillips"><EMAIL></string>
    <string name="persona_email_elliot_woodward"><EMAIL></string>
    <string name="persona_email_elvia_atkins"><EMAIL></string>
    <string name="persona_email_erik_nason"><EMAIL></string>
    <string name="persona_email_henry_brill"><EMAIL></string>
    <string name="persona_email_isaac_fielder"><EMAIL></string>
    <string name="persona_email_johnie_mcconnell"><EMAIL></string>
    <string name="persona_email_kat_larsson"><EMAIL></string>
    <string name="persona_email_katri_ahokas"><EMAIL></string>
    <string name="persona_email_kevin_sturgis"><EMAIL></string>
    <string name="persona_email_kristen_patterson"><EMAIL></string>
    <string name="persona_email_lydia_bauer"><EMAIL></string>
    <string name="persona_email_mauricio_august"><EMAIL></string>
    <string name="persona_email_miguel_garcia"><EMAIL></string>
    <string name="persona_email_mona_kane"><EMAIL></string>
    <string name="persona_email_robin_counts"><EMAIL></string>
    <string name="persona_email_tim_deboer"><EMAIL></string>
    <string name="persona_email_wanda_howard"><EMAIL></string>
    <string name="persona_footer">Available</string>
    <!-- Person names -->
    <string name="persona_name_allan_munger">Allan Munger</string>
    <string name="persona_name_amanda_brady">Amanda Brady</string>
    <string name="persona_name_ashley_mccarthy">Ashley McCarthy</string>
    <string name="persona_name_carlos_slattery">Carlos Slattery</string>
    <string name="persona_name_carole_poland">Carole Poland</string>
    <string name="persona_name_cecil_folk">Cecil Folk</string>
    <string name="persona_name_celeste_burton">Celeste Burton</string>
    <string name="persona_name_charlotte_waltson">Charlotte Waltson</string>
    <string name="persona_name_colin_ballinger">Colin Ballinger</string>
    <string name="persona_name_daisy_phillips">Daisy Phillips</string>
    <string name="persona_name_elliot_woodward">Elliot Woodward</string>
    <string name="persona_name_elvia_atkins">Elvia Atkins</string>
    <string name="persona_name_erik_nason">Erik Nason</string>
    <string name="persona_name_henry_brill">Henry Brill</string>
    <string name="persona_name_isaac_fielder">Isaac Fielder</string>
    <string name="persona_name_johnie_mcconnell">Johnie McConnell</string>
    <string name="persona_name_kat_larsson">Kat Larsson</string>
    <string name="persona_name_katri_ahokas">Katri Ahokas</string>
    <string name="persona_name_kevin_sturgis">Kevin Sturgis</string>
    <string name="persona_name_kristen_patterson">Kristen Patterson</string>
    <string name="persona_name_lydia_bauer">Lydia Bauer</string>
    <string name="persona_name_mauricio_august">Mauricio August</string>
    <string name="persona_name_miguel_garcia">Miguel Garcia</string>
    <string name="persona_name_mona_kane">Mona Kane</string>
    <string name="persona_name_robin_counts">Robin Counts</string>
    <string name="persona_name_robert_tolbert">Robert Tolbert</string>
    <string name="persona_name_tim_deboer">Tim Deboer</string>
    <string name="persona_name_wanda_howard">Wanda Howard</string>
    <!-- Designations -->
    <string name="persona_subtitle_designer">Designer</string>
    <string name="persona_subtitle_engineer">Engineer</string>
    <string name="persona_subtitle_manager">Manager</string>
    <string name="persona_subtitle_researcher">Researcher</string>
    <!-- UI labels -->
    <string name="persona_truncation">\ (long text example to test truncation)</string>
    <string name="persona_view_description_xxlarge">XXLarge avatar with three lines of text</string>
    <string name="persona_view_description_large">Large avatar with two lines of text</string>
    <string name="persona_view_description_small">Small avatar with one line of text</string>
    <string name="people_picker_hint">None with hint shown</string>

    <!--Persona Chip-->
    <!-- UI label showing a disabled persona chip -->
    <string name="persona_chip_example_disabled">Disabled Persona Chip</string>
    <!-- UI label showing a error persona chip -->
    <string name="persona_chip_example_error">Error Persona Chip</string>
    <!-- UI label showing a persona chip with no close icon-->
    <string name="persona_chip_example_no_icon">Persona Chip with no close icon</string>
    <!-- UI label showing a basic persona chip -->
    <string name="persona_chip_example_basic">Basic Persona Chip</string>
    <!-- UI label showing user action click on a selected persona chip -->
    <string name="persona_chip_example_click">You clicked on a selected Persona Chip.</string>

    <!--PopupMenu-->
    <!-- UI label for item share -->
    <string name="popup_menu_item_share">Share</string>
    <!-- UI label for item follow -->
    <string name="popup_menu_item_follow">Follow</string>
    <!-- UI label for item invite people -->
    <string name="popup_menu_item_invite_people">Invite people</string>
    <!-- UI label for item refresh page -->
    <string name="popup_menu_item_refresh_page">Refresh page</string>
    <!-- UI label for item open in browser -->
    <string name="popup_menu_item_open_in_browser">Open in browser</string>
    <!-- UI label for multi line description -->
    <string name="popup_menu_item_multiline">This is a multiline Popup Menu. Max lines are set to two, the rest of the text will truncate.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <!-- UI label for item All news -->
    <string name="popup_menu_item_all_news">All news</string>
    <!-- UI label for item Saved news -->
    <string name="popup_menu_item_saved_news">Saved news</string>
    <!-- UI label for item >News from sites -->
    <string name="popup_menu_item_news_from_sites">News from sites</string>
    <!-- UI label for item contoso -->
    <string name="popup_menu_item_contoso_travel">Contoso</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_outside">Notify outside of working hours</string>
    <!-- UI label for an item -->
    <string name="popup_menu_item_notify_inactive">Notify when inactive on desktop</string>
    <!-- UI message for user action clicked on an item in popup menu -->
    <string name="popup_menu_item_clicked">You clicked on the item:</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple">Simple menu</string>
    <!-- UI label that shows simple pop up menu -->
    <string name="popup_menu_simple_duo">Simple menu2</string>
    <!-- UI label that shows a pop up menu with one selectable item and a divider -->
    <string name="popup_menu_single_check">Menu with one selectable item and a divider</string>
    <!-- UI label that shows a pop up menu with all selectable items, icons, and long text -->
    <string name="popup_menu_all_check">Menu with all selectable items, icons, and long text</string>
    <!--Text for button that shows popupmenu -->
    <string name="popup_menu_show">Show</string>

    <!--Progress-->
    <!-- UI label showing a circular progress bar -->
    <string name="circular_progress">Circular Progress</string>
    <string name="circular_progress_xsmall">XSmall</string>
    <string name="circular_progress_xsmall_size">20dp</string>
    <string name="circular_progress_small">Small</string>
    <string name="circular_progress_small_size">24dp</string>
    <string name="circular_progress_medium">Medium</string>
    <string name="circular_progress_medium_size">36dp</string>
    <string name="circular_progress_large">Large</string>
    <string name="circular_progress_large_size">44dp</string>
    <!-- UI label showing a linear progress bar -->
    <string name="linear_progress">Linear Progress</string>
    <string name="linear_progress_size">4dp</string>
    <!-- UI label showing a Indeterminate progress bar -->
    <string name="linear_progress_indeterminate_label">Indeterminate</string>
    <!-- UI label showing a Determinate progress bar -->
    <string name="linear_progress_determinate_label">Determinate</string>


    <!--Shared-->
    <!-- dummy message -->
    <string name="long_placeholder">Lorem ipsum dolor sit amet, consectetur adipiscing elit,
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        Ut enim ad minim veniam.</string>

    <!--SearchBar-->
    <string name="searchbar">SearchBar</string>
    <!-- name of the icon-->
    <string name="searchbar_microphone_callback">Microphone Callback</string>
    <!-- UI Text label -->
    <string name="searchbar_autocorrect">Autocorrect</string>
    <!-- Announces that microphone has been pressed -->
    <string name="searchbar_microphone_pressed">Microphone Pressed</string>
    <!-- Announces that right view has been pressed -->
    <string name="searchbar_right_view_pressed">Right View Pressed</string>
    <!-- Announces that keyboard search has been pressed -->
    <string name="searchbar_keyboard_search_pressed">Keyboard Search Pressed</string>

    <!--Snackbar-->
    <!-- Snackbar Activity Buttons -->
    <string name="fluentui_show_snackbar">Show Snackbar</string>
    <string name="fluentui_dismiss_snackbar">Dismiss Snackbar</string>

    <!-- UI label descriptions -->
    <string name="snackbar_action">Action</string>
    <string name="snackbar_action_long">Long Text Action</string>
    <string name="snackbar_single_line">Single line snackbar</string>
    <string name="snackbar_multiline">This is a multiline snackbar. Max lines are set to two, the rest of the text will truncate.
        Lorem ipsum dolor sit amet consectetur adipiscing elit. </string>
    <string name="snackbar_announcement">This is an announcement snackbar. It’s used for communicating new features.</string>
    <string name="snackbar_primary">This is a primary snackbar.</string>
    <string name="snackbar_light">This is a light snackbar.</string>
    <string name="snackbar_warning">This is a warning snackbar.</string>
    <string name="snackbar_danger">This is a danger snackbar.</string>
    <string name="snackbar_description_single_line">Short duration</string>
    <string name="snackbar_description_single_line_custom_view">Long duration with circular progress as small custom view</string>
    <string name="snackbar_description_single_line_action">Short duration with action</string>
    <string name="snackbar_description_single_line_action_custom_view">Short duration with action and medium custom view</string>
    <string name="snackbar_description_single_line_custom_text_color">Short duration with customized text color</string>
    <string name="snackbar_description_multiline">Long duration</string>
    <string name="snackbar_description_multiline_custom_view">Long duration with small custom view</string>
    <string name="snackbar_description_multiline_action">Indefinite duration with action and text updates</string>
    <string name="snackbar_description_multiline_action_custom_view">Short duration with action and medium custom view</string>
    <string name="snackbar_description_multiline_action_long">Short duration with long action text</string>
    <string name="snackbar_description_announcement">Short duration</string>
    <string name="snackbar_description_updated">This text has been updated.</string>
    <!-- text for button that shows a snackbar -->
    <string name="snackbar_button_show">Show Snackbar</string>
    <!-- UI Text denoting single line snackbar -->
    <string name="snackbar_headline_single_line">Single line</string>
    <!-- UI Text denoting multi line snackbar -->
    <string name="snackbar_headline_multiline">Multiline</string>
    <!-- UI Text denoting Announcement style snackbar -->
    <string name="snackbar_headline_announcement_style">Announcement style</string>
    <!-- UI Text denoting Primary style snackbar -->
    <string name="snackbar_headline_primary_style">Primary style</string>
    <!-- UI Text denoting Light style snackbar -->
    <string name="snackbar_headline_light_style">Light style</string>
    <!-- UI Text denoting Warning stylee snackbar -->
    <string name="snackbar_headline_warning_style">Warning style</string>
    <!-- UI Text denoting Danger style snackbar -->
    <string name="snackbar_headline_danger_style">Danger style</string>

    <!-- TabBar-->
    <!-- UI labels -->
    <!-- text for tabbar home tab -->
    <string name="tabBar_home">Home</string>
    <!-- text for tabbar mail tab -->
    <string name="tabBar_mail">Mail</string>
    <!-- text for tabbar setting tab -->
    <string name="tabBar_settings">Settings</string>
    <!-- text for tabbar notification tab -->
    <string name="tabBar_notification">Notification</string>
    <!-- text for tabbar more tab -->
    <string name="tabBar_more">More</string>
    <!-- UI text for text alignment of tab -->
    <string name="tabBar_text_alignment">Text Alignment</string>
    <!-- UI text for vertical alignment of tab -->
    <string name="tabBar_vertical">Vertical</string>
    <!-- UI text for horizontal alignment of tab -->
    <string name="tabBar_horizontal">Horizontal</string>
    <!-- UI text for no text of tab -->
    <string name="tabBar_no_text">No Text</string>
    <!-- UI text for no. of tab items -->
    <string name="tabBar_tab_items">Tab Items</string>

    <!--TemplateView-->
    <!-- UI labels and descriptions -->
    <string name="cell_sample_title">Title</string>
    <string name="cell_sample_description">Description</string>
    <string name="calculate_cells">Load/calculate 100 cells</string>
    <string name="calculate_layouts">Load/calculate 100 layouts</string>
    <string name="template_list">Template List</string>
    <string name="regular_list">Regular List</string>
    <string name="cell_example_title">Title: Cell</string>
    <string name="cell_example_description">Description: Tap to change orientation</string>
    <string name="vertical_layout">Vertical Layout</string>
    <string name="horizontal_layout">Horizontal Layout</string>

    <!--TabLayout-->
    <!-- Text for button that shows a Standard Tab 2-Segment -->
    <string name="tab_standard_two_segment">Standard Tab 2-Segment</string>
    <!-- Text for button that shows a Standard Tab 3-Segment -->
    <string name="tab_standard_three_segment">Standard Tab 3-Segment</string>
    <!-- Text for button that shows a Standard Tab 4-Segment -->
    <string name="tab_standard_four_segment">Standard Tab 4-Segment</string>
    <!-- Text for button that shows a Standard Tab with pager -->
    <string name="tab_standard_with_pager">Standard Tab with Pager</string>
    <!-- Text for button that shows switch tabs -->
    <string name="tab_switch">Switch Tab</string>
    <!-- Text for button that shows pills tabs -->
    <string name="tab_pills">Pills Tab </string>

    <!--Tooltip-->
    <!-- Text for button that shows tooltip on tap -->
    <string name="tooltip_button">Tap for Tooltip</string>
    <!-- Text for button that shows Custom Calendar Tooltip on tap -->
    <string name="tooltip_calendar_demo_button">Tap for Custom Calendar Tooltip</string>
    <!-- Text for button that shows Custom Color Tooltip on tap -->
    <string name="tooltip_custom_button">Tap Custom Color Tooltip</string>
    <!-- Text for button that shows Dismiss Inside Tooltip on tap -->
    <string name="tooltip_center_button">Tap for Dismiss Inside Tooltip</string>
    <!-- Text for button that shows Custom View Tooltip on tap -->
    <string name="tooltip_custom_view">Tap for Custom View Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start_message">Top Custom Color Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end_message">Top End Tooltip with 10dp offsetX</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start_message">Bottom Start Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end_message">Bottom End Tooltip with 10dp offsetY</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center_message">Dismiss inside Tooltip</string>
    <!--UI label showing tooltip dismissed message -->
    <string name="tooltip_dismiss_message">Tooltip dismissed</string>

    <!--Typography-->
    <!-- UI label, headline with light appearance and font size 28sp -->
    <string name="headline">Headline is Light 28sp</string>
    <!-- UI label, title_1 with medium appearance and font size 20sp -->
    <string name="title_1">Title 1 is Medium 20sp</string>
    <!-- UI label, title_2 with Regular appearance and font size 20sp -->
    <string name="title_2">Title 2 is Regular 20sp</string>
    <!-- UI label, heading with Regular appearance and font size 18sp -->
    <string name="heading">Heading is Regular 18sp</string>
    <!-- UI label, subheading_1 with Regular appearance and font size 16sp -->
    <string name="subheading_1">Subheading 1 is Regular 16sp</string>
    <!-- UI label, subheading_2 with Medium appearance and font size 16sp -->
    <string name="subheading_2">Subheading 2 is Medium 16sp</string>
    <!-- UI label, body_1 with Regular appearance and font size 14sp -->
    <string name="body_1">Body 1 is Regular 14sp</string>
    <!-- UI label, body_2 with Medium appearance and font size 14sp -->
    <string name="body_2">Body 2 is Medium 14sp</string>
    <!-- UI label, caption with Regular appearance and font size 12sp -->
    <string name="caption">Caption is Regular 12sp</string>

    <!--Common-->
    <!-- UI Label for SDK Version -->
    <string name="sdk_version">SDK Version: %s</string>
    <!--UI Label for Toast message-->
    <string name="common_list">Item %d</string>
    <!--UI Label for Toast message-->
    <string name="common_folder">Folder</string>
    <!--UI Label for Toast message-->
    <string name="common_clicked">Clicked</string>

    <!--V2 Scaffold -->
    <!--UI Label Scaffold Example-->
    <string name="scaffold">Scaffold</string>
    <!--UI Label for button-->
    <string name="scaffold_fab">FAB</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_expanded">FAB Expanded</string>
    <!--UI Label for Toast message-->
    <string name="scaffold_fab_collapsed">FAB Collapsed</string>
    <!--UI Label for button-->
    <string name="scaffold_refresh_list">Click to refresh list</string>
    <!--UI Label for button-->
    <string name="scaffold_open_drawer">Open Drawer</string>

    <!--V2 Menu-->
    <!-- UI Label for content text-->
    <string name="menu_content_text_input">Menu Item</string>
    <!-- UI Label for xOffset -->
    <string name="menu_xOffset">Offset X (in dp)</string>
    <!-- UI Label for yOffset -->
    <string name="menu_yOffset">Offset Y (in dp)</string>
    <!-- UI Label for tip Offset-->
    <string name="menu_tipOffset">Tip Offset (in dp)</string>
    <!-- UI Label for content text -->
    <string name="menu_content_text">Content Text</string>
    <!-- UI Label for repeat content text -->
    <string name="menu_repeat_content_text">Repeat Content Text</string>
    <!-- UI Label for description -->
    <string name="menu_description" formatted="false">Menu width will change with respect to Content Text. The max
        width is restricted to 75% of screen size. The content margin from side and bottom is govern by token. The same content text will repeat to vary
        the height.</string>
    <!-- UI Label for open menu button -->
    <string name="menu_open_menu_button">Open Menu</string>

    <!--V2 Card-->
    <!-- UI Label for Card -->
    <string name="basic_card">Basic Card</string>
    <!-- UI Label for Card -->
    <string name="file_card">File Card</string>
    <!-- UI Label for Card -->
    <string name="announcement_card">Announcement Card</string>
    <!-- UI Label for button that generates random UI -->
    <string name="card_randomize">Random UI</string>
    <!-- UI Label for Options -->
    <string name="card_options">Options</string>
    <!-- UI Label for Title -->
    <string name="card_title">Title</string>
    <!-- UI Label for text -->
    <string name="card_text">Text</string>
    <!-- UI Label for sub text -->
    <string name="card_subtext">Sub Text</string>
    <!-- UI Label for a random description text -->
    <string name="card_description">Secondary copy for this banner can wrap to two lines if needed.</string>
    <!-- UI Label Button -->
    <string name="card_button">Button</string>

    <!-- V2 Dialog -->
    <!-- UI Label to display the dialog -->
    <string name="show_dialog">Show Dialog</string>
    <!-- UI label to close the dialog when clicked outside -->
    <string name="dismiss_dialog_outside">Dismiss dialog on clicking outside</string>
    <!-- UI label to close the dialog when back button is pressed -->
    <string name="dismiss_dialog_back">Dismiss dialog on back press</string>
    <!-- Text denoting a dialog is closed -->
    <string name="dismiss_dialog">Dialog dismissed</string>
    <!-- UI Label Cancel -->
    <string name="cancel">Cancel</string>
    <!-- UI Label Ok -->
    <string name="ok">Ok</string>
    <!-- A sample description -->
    <string name="dialog_description">A dialog is a small window that prompts the user to make a decision or enter additional information.</string>

    <!-- V2 Drawer -->
    <!-- UI Label for Button which open Drawer-->
    <string name="drawer_open">Open Drawer</string>
    <!-- UI Label for Button which expand Drawer-->
    <string name="drawer_expand">Expand Drawer</string>
    <!-- UI Label for Button which close Drawer-->
    <string name="drawer_close">Close Drawer</string>
    <!-- UI Label for Drawer Type Heading -->
    <string name="drawer_select_drawer_type">Select Drawer Type</string>
    <!-- UI Label for Top -->
    <string name="drawer_top">Top</string>
    <!-- UI Label for Top drawer description -->
    <string name="drawer_top_description">Whole drawer shows in the visible region.</string>
    <!-- UI Label for Bottom -->
    <string name="drawer_bottom">Bottom</string>
    <!-- UI Label for Bottom drawer description -->
    <string name="drawer_bottom_description">Whole drawer shows in the visible region. Swipe up motion scroll content. Expandable drawer expand via drag handle.</string>
    <!-- UI Label for Left Slide Over -->
    <string name="drawer_left_slide_over">Left Slide Over</string>
    <!-- UI Label for Left Slide Over drawer description -->
    <string name="drawer_left_slide_over_description">Drawer slide over to visible region from left side.</string>
    <!-- UI Label for Right Slide Over -->
    <string name="drawer_right_slide_over">Right Slide Over</string>
    <!-- UI Label for Right Slide Over drawer description -->
    <string name="drawer_right_slide_over_description">Drawer slide over to visible region from right side.</string>
    <!-- UI Label for Bottom Slide Over -->
    <string name="drawer_bottom_slide_over">Bottom Slide Over</string>
    <!-- UI Label for Bottom Slide Over drawer description -->
    <string name="drawer_bottom_slide_over_description">Drawer slide over to visible region from bottom of screen. Swipe up motion on expandable drawer bring its rest of part to visible region &amp; then scroll.</string>
    <!-- UI Label for Scrim Visible -->
    <string name="drawer_scrim_visible">Scrim Visible</string>
    <!-- UI Label for Select Drawer Content Heading-->
    <string name="drawer_select_drawer_content">Select Drawer Content</string>
    <!-- UI Label for Full screen size scrollable content text-->
    <string name="drawer_full_screen_size_scrollable_content">Full screen size scrollable content</string>
    <!-- UI Label for More than half screen content text-->
    <string name="drawer_more_than_half_screen_content">More than half screen content</string>
    <!-- UI Label for Less than half screen content text-->
    <string name="drawer_less_than_half_screen_content">Less than half screen content</string>
    <!-- UI Label for Dynamic size content text-->
    <string name="drawer_dynamic_size_content">Dynamic size content</string>
    <!-- UI Label for Nested drawer content text-->
    <string name="drawer_nested_drawer_content">Nested Drawer Content</string>
    <!-- UI Label for Expandable -->
    <string name="drawer_expandable">Expandable</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="skip_open_state">Skip Open State</string>
    <!-- UI Label for direct swipe down dismiss-->
    <string name="prevent_scrim_click_dismissal">Prevent Dismissal on Scrim Click</string>
    <!-- UI Label for Show Handle -->
    <string name="drawer_show_handle">Show Handle</string>
    <string name="drawer_enable_swipe_dismiss">Enable Swipe Dismiss</string>
    <string name="bottom_drawer_max_width_landscape">Max Landscape width</string>

    <!--V2 ToolTip-->
    <!-- UI Label for ToolTip Title-->
    <string name="tooltip_title">Title</string>
    <!-- UI Label for ToolTip Text-->
    <string name="tooltip_text">Tooltip Text</string>
    <!-- Text for button that shows Custom content Tooltip on tap -->
    <string name="tooltip_custom_content">Tap for Custom Content Tooltip</string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_start">Top Start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_top_end">Top End </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_start">Bottom Start </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_bottom_end">Bottom End </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_center">Center </string>
    <!--UI label showing tooltip message -->
    <string name="tooltip_custom_center">Custom Center</string>

    <!-- V2 Demo App -->
    <string name = "release_notes_updates">For updates on Release Notes,&#160;</string>
    <string name = "click_here">click here.</string>
    <string name = "open_source_cross_platform">Open source cross platform Design System.</string>
    <string name = "intuitive_and_powerful">Intuitive &amp; Powerful.</string>
    <string name = "design_tokens">Design Tokens</string>
    <string name = "release_notes">Release Notes</string>
    <string name = "github_repo">GitHub Repo</string>
    <string name = "github_repo_link">GitHub Repo Link</string>
    <string name = "report_issue">Report Issue</string>
    <string name = "v1_components">V1 Components</string>
    <string name = "v2_components">V2 Components</string>
    <string name = "all_components">All</string>
    <string name = "fluent_logo">Fluent Logo</string>
    <string name = "new_badge">New</string>
    <string name = "modified_badge">Modified</string>
    <string name = "api_break_badge">API Break</string>
    <string name = "app_bar_more">More</string>
    <string name = "accent">Accent</string>
    <string name = "appearance">Appearance</string>
    <string name = "choose_brand_theme">Choose your brand theme:</string>
    <string name = "fluent_brand_theme">Fluent Brand</string>
    <string name = "one_note_theme">OneNote</string>
    <string name = "word_theme">Word</string>
    <string name = "excel_theme">Excel</string>
    <string name = "powerpoint_theme">PowerPoint</string>
    <string name = "m365_theme">M365</string>
    <string name = "teams_theme">MS Teams</string>
    <string name = "choose_appearance">Choose Appearance</string>
    <string name = "appearance_system_default">System Default</string>
    <string name = "appearance_light">Light</string>
    <string name = "appearance_dark">Dark</string>
    <string name = "demo_activity_github_link">Demo Activity GitHub Link</string>
    <string name = "control_tokens_details">Control Tokens details</string>
    <string name = "parameters">Parameters</string>
    <string name = "control_tokens">Control Tokens</string>
    <string name = "global_tokens">Global Tokens</string>
    <string name = "alias_tokens">Alias Tokens</string>
    <string name = "sample_text">Text</string>
    <string name = "sample_icon">Sample Icon</string>
    <string name = "color">Color</string>
    <string name = "neutral_color_tokens">Neutral Color Tokens</string>
    <string name = "font_size_tokens">Font Size Tokens</string>
    <string name = "line_height_tokens">Line Height Tokens</string>
    <string name = "font_weight_tokens">Font Weight Tokens</string>
    <string name = "icon_size_tokens">Icon Size Tokens</string>
    <string name = "size_tokens">Size Tokens</string>
    <string name = "shadow_tokens">Shadow Tokens</string>
    <string name = "corner_radius_tokens">Corner RadiusTokens</string>
    <string name = "stroke_width_tokens">Stroke Width Tokens</string>
    <string name = "brand_color_tokens">Brand Color Tokens</string>
    <string name = "neutral_background_color_tokens">Neutral Background Color Tokens</string>
    <string name = "neutral_foreground_color_tokens">Neutral Foreground Color Tokens</string>
    <string name = "neutral_stroke_color_tokens">Neutral Stroke Color Tokens</string>
    <string name = "brand_background_color_tokens">Brand Background Color Tokens</string>
    <string name = "brand_foreground_color_tokens">Brand Foreground Color Tokens</string>
    <string name = "brand_stroke_color_tokens">Brand Stroke Color Tokens</string>
    <string name = "error_and_status_color_tokens">Error and Status Color Tokens</string>
    <string name = "presence_tokens">Presence Color Tokens</string>
    <string name = "typography_tokens">Typography Tokens</string>
    <string name = "unspecified">Unspecified</string>

</resources>