variables:
- group: InfoSec-SecurityResults
- name: tags
  value: production,externalfacing
resources:
  repositories:
  - repository: M365GPT
    type: git
    name: 1ESPipelineTemplates/M365GPT
    ref: refs/tags/release
extends:
  template: v1/M365.Official.PipelineTemplate.yml@M365GPT
  parameters:
    pool:
      name: Azure-Pipelines-1ESPT-ExDShared
      image: windows-2022
      os: windows
    customBuildTags:
    - ES365AIMigrationTooling
    stages:
    - stage: __default
      jobs:
      - job: Compliance
        displayName: Compliance checks
        steps:
        - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
          inputs:
            repository: self
            clean: true
            fetchDepth: 10
      - job: ''
        dependsOn:
        - Compliance
        variables:
        - name: BUILDSECMON_OPT_IN
          value: true
        steps:
        - task: Gradle@2
          inputs:
            gradleWrapperFile: 'gradlew'
            tasks: 'assembleRelease makeUniversalPkg'
            publishJUnitResults: true
            testResultsFiles: '**/TEST-*.xml'
            javaHomeOption: 'JDKVersion'
            jdkVersionOption: '1.11'
            sonarQubeRunAnalysis: false
            options: '-PappCenterSecret=$(appCenterSecret) -PsigningKeyPassword=$(signingKeyPassword) -PsigningKeyStorePassword=$(signingKeyStorePassword)'
        - task: CopyFiles@2
          displayName: 'Copy Files to: $(build.artifactstagingdirectory)'
          inputs:
            SourceFolder: '$(system.defaultworkingdirectory)/universal'
            Contents: '**'
            TargetFolder: '$(build.artifactstagingdirectory)/outputs'
            CleanTargetFolder: true
            OverWrite: true
            preserveTimestamp: true
        - task: UniversalPackages@0
          inputs:
            command: 'publish'
            publishDirectory: '$(Build.ArtifactStagingDirectory)/outputs/'
            feedsToUsePublish: 'internal'
            vstsFeedPublish: 'Office'
            vstsFeedPackagePublish: 'fluentuiandroid'
            versionOption: 'custom'
            versionPublish: '0.3.8'
            packagePublishDescription: 'Fluent Universal Package'
            publishedPackageVar: 'fluent package'
