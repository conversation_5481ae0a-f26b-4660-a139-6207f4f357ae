/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */

apply plugin: 'com.android.application'

apply plugin: 'kotlin-android'

android {
    compileSdkVersion constants.compileSdkVersion
    defaultConfig {
        applicationId 'com.microsoft.fluentuidemo'
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 2008
        versionName '0.3.8'
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion composeCompilerVersion
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        lintConfig = file("lint.xml")
        abortOnError false
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    signingConfigs {
        dogfood {
            keyAlias 'Dogfood'
            keyPassword = project.hasProperty("signingKeyPassword") ? "$signingKeyPassword" : ""
            storeFile file('keystore.jks')
            storePassword = project.hasProperty("signingKeyStorePassword") ? "$signingKeyStorePassword" : ""
        }
    }
    flavorDimensions 'distribution'
    productFlavors {
        development {
            dimension 'distribution'
        }
        dogfood {
            dimension 'distribution'
            applicationIdSuffix '.df'
            signingConfig signingConfigs.dogfood
            buildConfigField "String", "APP_CENTER_SECRET", project.hasProperty("appCenterSecret") ? "\"$appCenterSecret\"" : "\"\""
        }
    }
    testOptions {
        unitTests{
            includeAndroidResources = true
        }
        unitTests.all {
            testLogging {
                events "passed", "failed", "standardError"
                showCauses true
                showExceptions true
                showStackTraces true
            }
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':FluentUI')

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.appcompat:appcompat:$appCompatVersion"
    implementation "com.google.android.material:material:$materialVersion"
    implementation "androidx.exifinterface:exifinterface:$exifInterfaceVersion"
    implementation "androidx.recyclerview:recyclerview:$recyclerViewVersion"
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation "androidx.constraintlayout:constraintlayout:$constraintLayoutVersion"
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'com.github.bumptech.glide:glide:4.8.0'

    //Compose BOM
    implementation platform("androidx.compose:compose-bom:$composeBomVersion")
    implementation "androidx.compose.ui:ui"
    implementation ("androidx.compose.foundation:foundation-layout")
    implementation ("androidx.compose.material:material")
    implementation ("androidx.activity:activity-compose:$composeActivityVersion")
    implementation "androidx.test.ext:junit-ktx:$junitKtxVersion"
    implementation "androidx.test.uiautomator:uiautomator:$uiautomatorVersion"
    androidTestImplementation("androidx.compose.ui:ui-test-junit4:$composeTestVersion")
    debugImplementation("androidx.compose.ui:ui-test-manifest")
    debugImplementation "androidx.test:monitor:$androidTestMonitor"
    implementation "androidx.compose.runtime:runtime-livedata"

    // App Center
    dogfoodImplementation "com.microsoft.appcenter:appcenter-analytics:$appCenterSdkVersion"
    dogfoodImplementation "com.microsoft.appcenter:appcenter-crashes:$appCenterSdkVersion"
    dogfoodImplementation "com.microsoft.appcenter:appcenter-distribute:$appCenterSdkVersion"

    //JUnit4 framework
    testImplementation "junit:junit:${junitVersion}"
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"

    //Espresso
    androidTestImplementation "androidx.test.espresso:espresso-core:${espressoVersion}"
    androidTestImplementation "androidx.test.espresso:espresso-contrib:${espressoVersion}"
    androidTestImplementation "androidx.test.espresso:espresso-intents:${espressoVersion}"
    androidTestImplementation "androidx.test:runner:${androidxRunner}"
    androidTestImplementation "androidx.test:rules:${androidxRunner}"
    androidTestImplementation "org.hamcrest:hamcrest:$hamcrestVersion"

    implementation 'com.microsoft.design:fluent-system-icons:1.1.203@aar'

    //Robolectric
    testImplementation "org.robolectric:robolectric:${robolectric}"
}
